#!/usr/bin/env python3
"""
Simple test script to verify the audio fixes work correctly
"""

import sys
import time

def test_audio_imports():
    """Test that all audio-related imports work"""
    try:
        print("🔄 Testing audio imports...")
        
        # Test PyAudio imports
        try:
            import pyaudiowpatch as pyaudio
            print("✅ PyAudioWPatch available")
        except ImportError:
            import pyaudio
            print("✅ Standard PyAudio available")
        
        # Test other imports
        import concurrent.futures
        import threading
        import queue
        import weakref
        
        print("✅ All imports successful")
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_pyaudio_manager():
    """Test PyAudioManager functionality"""
    try:
        print("🔄 Testing PyAudioManager...")
        
        # Import the PyAudioManager from live.py
        sys.path.append('.')
        from live import PyAudioManager
        
        # Create instance
        manager = PyAudioManager()
        print("✅ PyAudioManager created successfully")
        
        # Test getting audio instance
        audio = manager.get_audio_instance()
        if audio:
            print("✅ Audio instance created successfully")
            
            # Test device count
            device_count = manager.get_device_count_safe(audio)
            print(f"✅ Found {device_count} audio devices")
            
            # Test device info for first few devices
            for i in range(min(3, device_count)):
                device_info = manager.get_device_info_safe(audio, i)
                if device_info:
                    print(f"   Device {i}: {device_info['name']}")
                    is_bluetooth = manager.is_bluetooth_device(device_info)
                    if is_bluetooth:
                        print(f"     🔵 Bluetooth device detected")
            
            # Cleanup
            audio.terminate()
            print("✅ Audio instance cleaned up")
        
        # Test cleanup
        manager.cleanup()
        print("✅ PyAudioManager cleanup successful")
        
        return True
        
    except Exception as e:
        print(f"❌ PyAudioManager test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_audio():
    """Test SystemAudioCapture functionality"""
    try:
        print("🔄 Testing SystemAudioCapture...")
        
        from live import SystemAudioCapture
        
        # Create instance
        system_audio = SystemAudioCapture()
        print("✅ SystemAudioCapture created successfully")
        
        # Test availability
        if system_audio.is_available():
            print("✅ System audio capture is available")
            
            # Test device info
            device_info = system_audio.get_device_info()
            if device_info:
                print(f"   Device: {device_info['name']}")
            
            # Test loopback devices
            loopback_devices = system_audio.get_available_loopback_devices()
            print(f"✅ Found {len(loopback_devices)} loopback devices")
            
        else:
            print("⚠️ System audio capture not available (this is OK)")
        
        # Cleanup
        system_audio.cleanup()
        print("✅ SystemAudioCapture cleanup successful")
        
        return True
        
    except Exception as e:
        print(f"❌ SystemAudioCapture test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Starting audio system tests...\n")
    
    tests = [
        ("Audio Imports", test_audio_imports),
        ("PyAudioManager", test_pyaudio_manager),
        ("SystemAudioCapture", test_system_audio),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Testing: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
                
        except Exception as e:
            print(f"❌ {test_name} test CRASHED: {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # Small delay between tests
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Audio system is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
