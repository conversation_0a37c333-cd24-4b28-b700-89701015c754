#!/usr/bin/env python3
"""Quick test for system audio capture"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from live import SystemAudioCapture
    print("✅ SystemAudioCapture imported")
    
    # Test initialization
    system_audio = SystemAudioCapture()
    print(f"✅ Initialized. Available: {system_audio.is_available()}")
    
    if system_audio.is_available():
        # Test opening stream
        if system_audio.open_stream():
            print("✅ Stream opened successfully")
            
            # Test reading a few chunks
            for i in range(5):
                chunk = system_audio.read_audio_chunk()
                if chunk:
                    print(f"✅ Read chunk {i+1}: {len(chunk)} bytes")
                else:
                    print(f"❌ Failed to read chunk {i+1}")
            
            system_audio.close_stream()
            print("✅ Stream closed")
        else:
            print("❌ Failed to open stream")
    
    system_audio.cleanup()
    print("✅ Test completed")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
