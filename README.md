# InterV AI - Interview Assistant

A production-ready AI-powered interview assistant with real-time speech-to-text, multi-AI responses, and stealth screen sharing capabilities. Perfect for job interviews, meetings, and professional conversations.

## Features

- **Fully Automatic**: No start/stop buttons - automatically starts listening when application opens
- **Real-time Speech Recognition**: Uses OpenAI Whisper for accurate speech-to-text conversion
- **Multi-AI Response**: Uses 3 AI models with fallback (Gemini → Mistral → OpenRouter) and shows which model responded
- **Screen Capture Stealth**: Hidden from screen sharing while remaining visible locally
- **Windows 10/11 Compatible**: Works on both Windows 10 and Windows 11
- **Popup Window Interface**: Clean, draggable popup window interface
- **Auto-positioning**: Automatically positions at bottom-right corner
- **Always Active**: Continuously listens and responds - perfect for interviews and meetings
- **User Authentication**: Firebase-based login system with time-based usage tracking
- **Time Management**: Tracks usage time and enforces time limits per user

## Installation

1. **Install Dependencies**:
   ```bash
   # Run the setup script
   setup.bat
   
   # Or manually install
   pip install -r requirements.txt
   ```

2. **Configure API Key**:
   - The application uses the same API key as ANSARI_AI_V2
   - Google Gemini API key is already configured in the code
   - No additional configuration needed

## Usage

1. **Start the Application**:
   ```bash
   python live.py
   ```

2. **Using the Interface**:
   - Application automatically starts listening when opened
   - Simply speak into your microphone
   - Real-time text appears in the "Real-time Text" section
   - When you stop speaking (2 seconds of silence), the complete text is sent to AI
   - AI response appears in the "AI Response" section
   - No manual start/stop required - always active!

3. **Window Controls**:
   - **Drag**: Click and drag the window to move it
   - **Resize**: Use the resize grip in bottom-right corner
   - **Close**: Click the ✕ button
   - **Keyboard Shortcuts**:
     - `Ctrl+Space`: Toggle window visibility (hide/show)
     - `Ctrl+Arrow Keys`: Hold to move window continuously (smooth movement)

## Stealth Features

- **Screen Capture Hiding**: Window is hidden from screen recording/sharing
- **Local Visibility**: Remains visible to you on your local screen
- **Taskbar Visibility**: Optionally visible in taskbar (configurable)
- **Always on Top**: Stays on top of other windows

## Configuration

You can modify these settings in the code:

```python
# Stealth Configuration
WINDOW_TRANSPARENCY = 0.90          # Window transparency (0.0-1.0)
ENABLE_SCREEN_CAPTURE_HIDING = True # Hide from screen capture
ENABLE_TASKBAR_HIDING = False       # Hide from taskbar
LOCAL_VISIBILITY_MODE = True        # Keep visible locally
```

## Requirements

- **Python 3.7+**
- **Windows 10/11**
- **Microphone access**
- **Internet connection** (for AI responses)

## Troubleshooting

1. **Audio Issues**:
   - Check microphone permissions
   - Ensure microphone is working in other applications
   - Try adjusting `SILENCE_THRESHOLD` in the code

2. **API Issues**:
   - Check internet connection
   - Verify API key is valid
   - Check API quota/limits

3. **Window Not Hiding**:
   - Ensure you're running on Windows 10/11
   - Try running as administrator
   - Check if screen sharing software supports the hiding method

## Technical Details

- **Speech Recognition**: OpenAI Whisper (base model)
- **AI API**: Google Gemini 1.5 Flash
- **GUI Framework**: PyQt5
- **Audio Processing**: PyAudio
- **Screen Hiding**: Windows Display Affinity API

## Similar to ANSARI_AI_V2

This application implements the same stealth features and API configuration as ANSARI_AI_V2:
- Same API keys and endpoints
- Same screen capture hiding methodology
- Same window positioning and transparency
- Same local visibility while hidden from capture
