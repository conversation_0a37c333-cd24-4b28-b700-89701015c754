# Troubleshooting Guide

## Common Issues and Solutions

### 1. Real-time Text Not Showing

**Problem**: Application starts but no text appears in the "Real-time Text" section when speaking.

**Solutions**:

1. **Check Microphone Permissions**:
   - Go to Windows Settings > Privacy > Microphone
   - Make sure "Allow apps to access your microphone" is ON
   - Make sure Python is allowed to access microphone

2. **Test Audio Devices**:
   ```bash
   python test_audio.py
   ```
   This will show available audio devices and test microphone levels.

3. **Check Audio Levels**:
   - Speak loudly and clearly into microphone
   - Check if audio levels show in the test script
   - If levels are too low, increase microphone volume in Windows

4. **Adjust Sensitivity**:
   - Open `live.py` in a text editor
   - Find line: `self.SILENCE_THRESHOLD = 200`
   - Try lower values like 100 or 50 for higher sensitivity
   - Try higher values like 500 or 1000 for lower sensitivity

### 2. AI Response Not Working

**Problem**: Text is transcribed but no AI response appears.

**Solutions**:

1. **Check Internet Connection**:
   - Make sure you have active internet connection
   - Test by opening a website in browser

2. **Check API Key**:
   - The Google Gemini API key is pre-configured
   - If it stops working, you may need to get a new API key

3. **Check Console Output**:
   - Look at the terminal/console where you ran `python live.py`
   - Check for error messages related to API calls

### 3. Window Not Visible to Me

**Problem**: Application starts but window is not visible on my screen.

**Solutions**:

1. **Use Keyboard Shortcut**:
   - Press `Ctrl+Space` to toggle window visibility
   - This should bring the window back if it's hidden

2. **Use Emergency Show Tool**:
   ```bash
   python show_window.py
   ```
   This will find and show the window if it's hidden.

3. **Check Window Position**:
   - Window might be off-screen
   - Press `Ctrl+Arrow keys` to move window back to visible area
   - Or restart application to reset position

4. **Restart Application**:
   - Close current application (Ctrl+C in terminal)
   - Run again: `python live.py`

### 4. Window Not Hiding from Screen Share

**Problem**: Window is visible during screen sharing.

**Solutions**:

1. **Windows Version**:
   - This feature works best on Windows 10 version 1903+ and Windows 11
   - Older versions may not support this feature

2. **Run as Administrator**:
   - Right-click Command Prompt and select "Run as administrator"
   - Navigate to the project folder and run: `python live.py`

3. **Check Screen Sharing Software**:
   - Some screen sharing software may override Windows hiding features
   - Test with different screen sharing applications (Teams, Zoom, Discord, etc.)

### 4. Application Won't Start

**Problem**: Application crashes or won't start.

**Solutions**:

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Check Python Version**:
   - Make sure you have Python 3.7 or higher
   - Check with: `python --version`

3. **Install PyAudio Manually**:
   ```bash
   pip install pipwin
   pipwin install pyaudio
   ```

4. **Install Visual C++ Redistributable**:
   - Download and install Microsoft Visual C++ Redistributable
   - Required for some audio libraries

### 5. Audio Quality Issues

**Problem**: Poor transcription accuracy.

**Solutions**:

1. **Use Better Microphone**:
   - Use a headset or external microphone
   - Avoid built-in laptop microphones if possible

2. **Reduce Background Noise**:
   - Use the application in a quiet environment
   - Close other applications that might use microphone

3. **Speak Clearly**:
   - Speak slowly and clearly
   - Pause between sentences
   - Avoid speaking too fast

4. **Adjust Whisper Model**:
   - Open `live.py` and find: `self.model = whisper.load_model("base")`
   - Try "small", "medium", or "large" for better accuracy (but slower processing)

### 6. Performance Issues

**Problem**: Application is slow or laggy.

**Solutions**:

1. **Use Smaller Whisper Model**:
   - Change from "base" to "tiny" for faster processing
   - Trade-off: faster but less accurate

2. **Close Other Applications**:
   - Close unnecessary applications to free up CPU and memory
   - Especially close other audio/video applications

3. **Increase Chunk Duration**:
   - In `live.py`, find: `self.CHUNK_DURATION = 1.0`
   - Try 2.0 or 3.0 for less frequent processing

## Debug Mode

To enable detailed debug output:

1. Open `live.py`
2. Find: `DEBUG_STEALTH_FEATURES = False`
3. Change to: `DEBUG_STEALTH_FEATURES = True`
4. Save and restart the application

This will show detailed information about what the application is doing.

## Getting Help

If none of these solutions work:

1. Run the application from command prompt to see error messages
2. Check the console output for specific error details
3. Make sure all requirements are properly installed
4. Try running `test_audio.py` to isolate audio issues

## System Requirements

- **OS**: Windows 10 (version 1903+) or Windows 11
- **Python**: 3.7 or higher
- **RAM**: At least 4GB (8GB recommended)
- **Internet**: Required for AI responses
- **Microphone**: Any working microphone (headset recommended)
