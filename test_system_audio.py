#!/usr/bin/env python3
"""
Test script for system audio capture functionality.
This script validates that the WASAPI loopback capture is working correctly.
"""

import sys
import time
import wave
import threading
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

try:
    # Import our system audio capture class
    from live import SystemAudioCapture, PYAUDIO_WPATCH_AVAILABLE
    print("✅ Successfully imported SystemAudioCapture")
except ImportError as e:
    print(f"❌ Failed to import SystemAudioCapture: {e}")
    sys.exit(1)

def test_pyaudiowpatch_availability():
    """Test if PyAudioWPatch is available"""
    print("\n🔍 Testing PyAudioWPatch availability...")
    
    if PYAUDIO_WPATCH_AVAILABLE:
        print("✅ PyAudioWPatch is available")
        try:
            import pyaudiowpatch as pyaudio
            with pyaudio.PyAudio() as p:
                # Test WASAPI availability
                try:
                    wasapi_info = p.get_host_api_info_by_type(pyaudio.paWASAPI)
                    print(f"✅ WASAPI available: {wasapi_info['name']}")
                    return True
                except OSError:
                    print("❌ WASAPI not available on this system")
                    return False
        except Exception as e:
            print(f"❌ Error testing PyAudioWPatch: {e}")
            return False
    else:
        print("❌ PyAudioWPatch not available - falling back to standard PyAudio")
        return False

def test_system_audio_capture():
    """Test system audio capture initialization"""
    print("\n🔍 Testing SystemAudioCapture initialization...")
    
    try:
        # Initialize system audio capture
        system_audio = SystemAudioCapture()
        
        if system_audio.is_available():
            print("✅ SystemAudioCapture initialized successfully")
            
            # Get device info
            device_info = system_audio.get_device_info()
            if device_info:
                print(f"✅ Default loopback device: {device_info['name']}")
                print(f"   Sample Rate: {device_info.get('defaultSampleRate', 'Unknown')} Hz")
                print(f"   Max Input Channels: {device_info.get('maxInputChannels', 'Unknown')}")
            
            # Get available loopback devices
            devices = system_audio.get_available_loopback_devices()
            print(f"✅ Found {len(devices)} loopback devices:")
            for idx, name in devices:
                print(f"   [{idx}] {name}")
            
            return system_audio
        else:
            print("❌ SystemAudioCapture not available")
            return None
            
    except Exception as e:
        print(f"❌ Error initializing SystemAudioCapture: {e}")
        return None

def test_audio_stream(system_audio, duration=5):
    """Test opening and reading from audio stream"""
    print(f"\n🔍 Testing audio stream for {duration} seconds...")
    
    try:
        # Open stream
        if not system_audio.open_stream():
            print("❌ Failed to open audio stream")
            return False
        
        print("✅ Audio stream opened successfully")
        
        # Read audio chunks for specified duration
        start_time = time.time()
        chunk_count = 0
        total_bytes = 0
        
        while time.time() - start_time < duration:
            audio_chunk = system_audio.read_audio_chunk()
            if audio_chunk:
                chunk_count += 1
                total_bytes += len(audio_chunk)
            else:
                print("⚠️ Received empty audio chunk")
            
            time.sleep(0.01)  # Small delay to avoid overwhelming the system
        
        print(f"✅ Successfully read {chunk_count} audio chunks")
        print(f"✅ Total audio data: {total_bytes} bytes")
        print(f"✅ Average chunk size: {total_bytes // chunk_count if chunk_count > 0 else 0} bytes")
        
        # Close stream
        system_audio.close_stream()
        print("✅ Audio stream closed successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing audio stream: {e}")
        return False

def test_record_to_file(system_audio, filename="test_system_audio.wav", duration=3):
    """Test recording system audio to a file"""
    print(f"\n🔍 Testing recording to file '{filename}' for {duration} seconds...")
    
    try:
        # Open stream
        if not system_audio.open_stream():
            print("❌ Failed to open audio stream")
            return False
        
        # Get audio settings
        device_info = system_audio.get_device_info()
        sample_rate = int(device_info.get('defaultSampleRate', 44100))
        channels = 1  # SystemAudioCapture converts to mono
        
        # Create WAV file
        with wave.open(filename, 'wb') as wav_file:
            wav_file.setnchannels(channels)
            wav_file.setsampwidth(2)  # 16-bit
            wav_file.setframerate(sample_rate)
            
            print(f"📁 Recording to {filename}...")
            print(f"   Sample Rate: {sample_rate} Hz")
            print(f"   Channels: {channels}")
            print(f"   Duration: {duration} seconds")
            
            # Record audio
            start_time = time.time()
            frames_recorded = 0
            
            while time.time() - start_time < duration:
                audio_chunk = system_audio.read_audio_chunk()
                if audio_chunk:
                    wav_file.writeframes(audio_chunk)
                    frames_recorded += 1
                
                time.sleep(0.01)
            
            print(f"✅ Recorded {frames_recorded} frames to {filename}")
        
        # Close stream
        system_audio.close_stream()
        
        # Check file size
        file_size = Path(filename).stat().st_size
        print(f"✅ File created: {filename} ({file_size} bytes)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error recording to file: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 System Audio Capture Test Suite")
    print("=" * 50)
    
    # Test 1: PyAudioWPatch availability
    if not test_pyaudiowpatch_availability():
        print("\n❌ PyAudioWPatch not available - system audio capture will not work")
        print("💡 Install PyAudioWPatch: pip install PyAudioWPatch")
        return False
    
    # Test 2: SystemAudioCapture initialization
    system_audio = test_system_audio_capture()
    if not system_audio:
        print("\n❌ SystemAudioCapture initialization failed")
        return False
    
    # Test 3: Audio stream functionality
    if not test_audio_stream(system_audio, duration=3):
        print("\n❌ Audio stream test failed")
        return False
    
    # Test 4: Record to file
    if not test_record_to_file(system_audio, duration=2):
        print("\n❌ Recording test failed")
        return False
    
    # Cleanup
    system_audio.cleanup()
    
    print("\n" + "=" * 50)
    print("✅ All tests passed! System audio capture is working correctly.")
    print("🎉 The application should now be able to capture system audio without stereo mix.")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
