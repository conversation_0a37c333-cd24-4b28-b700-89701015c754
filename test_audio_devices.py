#!/usr/bin/env python3
"""
Test script for audio device detection and management
This script tests the automatic audio device functionality without running the full application
"""

import sys
import time
import pyaudio
from typing import Optional, Callable, List, Tuple, Dict

# Platform-specific imports for audio device management
if sys.platform == "win32":
    try:
        from pycaw.pycaw import AudioUtilities, AudioEndpointVolume
        from comtypes import CLSCTX_ALL
        import psutil
        WINDOWS_AUDIO_AVAILABLE = True
        print("✅ Windows audio libraries available")
    except ImportError as e:
        WINDOWS_AUDIO_AVAILABLE = False
        print(f"⚠️ Windows audio monitoring libraries not available: {e}. Using fallback detection.")
    except Exception as e:
        WINDOWS_AUDIO_AVAILABLE = False
        print(f"⚠️ Error importing Windows audio libraries: {e}. Using fallback detection.")
else:
    WINDOWS_AUDIO_AVAILABLE = False
    print("ℹ️ Non-Windows platform detected")

class AudioDeviceTest:
    """Test class for audio device functionality"""
    
    def __init__(self):
        self.audio = pyaudio.PyAudio()
    
    def test_device_enumeration(self):
        """Test basic device enumeration"""
        print("\n🔍 Testing device enumeration...")
        try:
            device_count = self.audio.get_device_count()
            print(f"Found {device_count} audio devices")
            
            input_devices = []
            for i in range(device_count):
                try:
                    dev = self.audio.get_device_info_by_index(i)
                    if dev['maxInputChannels'] > 0:
                        input_devices.append((i, dev['name']))
                        print(f"  Input Device {i}: {dev['name']}")
                except Exception as e:
                    print(f"  Error reading device {i}: {e}")
            
            print(f"✅ Found {len(input_devices)} input devices")
            return input_devices
            
        except Exception as e:
            print(f"❌ Error enumerating devices: {e}")
            return []
    
    def test_default_device_detection(self):
        """Test default device detection"""
        print("\n🎯 Testing default device detection...")
        
        # Test fallback method
        try:
            default_device_info = self.audio.get_default_input_device_info()
            if default_device_info:
                print(f"✅ PyAudio default device: {default_device_info['name']}")
            else:
                print("⚠️ No default device found via PyAudio")
        except Exception as e:
            print(f"⚠️ PyAudio default device detection failed: {e}")
        
        # Test Windows-specific method
        if sys.platform == "win32" and WINDOWS_AUDIO_AVAILABLE:
            try:
                devices = AudioUtilities.GetMicrophone()
                if devices:
                    print(f"✅ Windows default device: {devices.FriendlyName}")
                else:
                    print("⚠️ No default device found via Windows API")
            except Exception as e:
                print(f"⚠️ Windows default device detection failed: {e}")
    
    def test_device_opening(self, device_index=None):
        """Test opening an audio stream on a specific device"""
        print(f"\n🎤 Testing audio stream opening (device {device_index})...")
        
        try:
            stream = self.audio.open(
                format=pyaudio.paInt16,
                channels=1,
                rate=16000,
                input=True,
                frames_per_buffer=1024,
                input_device_index=device_index
            )
            
            print("✅ Audio stream opened successfully")
            
            # Test reading a few chunks
            print("📊 Testing audio input...")
            for i in range(5):
                try:
                    data = stream.read(1024, exception_on_overflow=False)
                    print(f"  Chunk {i+1}: {len(data)} bytes read")
                    time.sleep(0.1)
                except Exception as e:
                    print(f"  ❌ Error reading chunk {i+1}: {e}")
                    break
            
            stream.stop_stream()
            stream.close()
            print("✅ Audio stream closed successfully")
            return True
            
        except Exception as e:
            print(f"❌ Error opening audio stream: {e}")
            return False
    
    def run_full_test(self):
        """Run complete audio device test suite"""
        print("🚀 Starting Audio Device Test Suite")
        print("=" * 50)
        
        # Test 1: Device enumeration
        input_devices = self.test_device_enumeration()
        
        # Test 2: Default device detection
        self.test_default_device_detection()
        
        # Test 3: Stream opening with default device
        print("\n🎤 Testing default device stream...")
        default_success = self.test_device_opening(None)
        
        # Test 4: Stream opening with first available device
        if input_devices and not default_success:
            print(f"\n🎤 Testing first available device (index {input_devices[0][0]})...")
            self.test_device_opening(input_devices[0][0])
        
        print("\n" + "=" * 50)
        print("🏁 Audio Device Test Complete")
        
        # Summary
        if input_devices:
            print(f"✅ {len(input_devices)} input devices detected")
            print("✅ Basic audio functionality working")
            print("\n💡 The live.py application should work with automatic device detection!")
        else:
            print("❌ No input devices found")
            print("⚠️ Check your audio device connections and permissions")
    
    def cleanup(self):
        """Clean up resources"""
        if hasattr(self, 'audio') and self.audio:
            self.audio.terminate()

def main():
    """Main test function"""
    test = AudioDeviceTest()
    try:
        test.run_full_test()
    finally:
        test.cleanup()

if __name__ == "__main__":
    main()
