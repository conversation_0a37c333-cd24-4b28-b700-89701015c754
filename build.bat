@echo off
echo ========================================
echo InterV AI - LIGHTWEIGHT Build Script
echo ========================================
echo.

echo Installing LIGHTWEIGHT packages...
pip install pyinstaller PyQt5 pyaudio faster-whisper numpy requests firebase-admin

echo.
echo Cleaning previous builds...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist *.spec del *.spec
if exist __pycache__ rmdir /s /q __pycache__

echo Building ULTRA-LIGHTWEIGHT executable...
pyinstaller --onefile --noconsole ^
    --hidden-import=faster_whisper ^
    --hidden-import=PyQt5.QtCore ^
    --hidden-import=PyQt5.QtGui ^
    --hidden-import=PyQt5.QtWidgets ^
    --hidden-import=pyaudio ^
    --hidden-import=numpy ^
    --hidden-import=firebase_admin ^
    --hidden-import=firebase_admin.credentials ^
    --hidden-import=firebase_admin.firestore ^
    --exclude-module=torch ^
    --exclude-module=tensorflow ^
    --exclude-module=matplotlib ^
    --exclude-module=scipy ^
    --exclude-module=pandas ^
    --exclude-module=jupyter ^
    --exclude-module=notebook ^
    --exclude-module=IPython ^
    --exclude-module=cv2 ^
    --exclude-module=PIL ^
    --optimize=2 ^
    --strip ^
    --name=InterV_AI_Ultra_Lite ^
    live.py

echo.
echo ========================================
echo ULTRA-LIGHTWEIGHT Build completed!
echo ========================================
echo.
echo Executable location: dist\InterV_AI_Ultra_Lite.exe
echo.
echo Features included:
echo - Firebase Authentication
echo - Real-time Speech Recognition
echo - AI Response Generation
echo - Time Tracking and Auto-logout
echo - Stealth Mode
echo.
echo File size should be under 100MB!
echo.
pause
echo Check dist folder for InterV_AI_Lite.exe (should be ~50-80MB instead of 350MB)
echo.
pause
