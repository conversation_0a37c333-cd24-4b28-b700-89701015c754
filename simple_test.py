#!/usr/bin/env python3
"""Simple test for system audio capture"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from live import SystemAudioCapture
    print("✅ SystemAudioCapture imported successfully")
    
    # Test initialization
    system_audio = SystemAudioCapture()
    print(f"✅ SystemAudioCapture initialized")
    print(f"   Available: {system_audio.is_available()}")
    
    if system_audio.is_available():
        device_info = system_audio.get_device_info()
        if device_info:
            print(f"   Device: {device_info['name']}")
            print(f"   Sample Rate: {device_info.get('defaultSampleRate', 'Unknown')}")
        
        # List available devices
        devices = system_audio.get_available_loopback_devices()
        print(f"   Found {len(devices)} loopback devices")
        for idx, name in devices[:3]:  # Show first 3
            print(f"     [{idx}] {name}")
    
    system_audio.cleanup()
    print("✅ Test completed successfully")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
