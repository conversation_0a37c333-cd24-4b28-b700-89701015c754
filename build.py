#!/usr/bin/env python3
"""
InterV AI - Complete Build Script
Automatically installs packages and creates optimized executable
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, description):
    """Run command with proper error handling"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"Output: {e.stdout}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def install_packages():
    """Install all required packages"""
    packages = [
        "pyinstaller",
        "PyQt5", 
        "pyaudio",
        "faster-whisper",
        "numpy",
        "requests",
        "firebase-admin",
        "wave",
        "threading",
        "json",
        "traceback"
    ]
    
    print("📦 Installing required packages...")
    for package in packages:
        command = f"pip install {package}"
        if not run_command(command, f"Installing {package}"):
            print(f"⚠️ Warning: Failed to install {package}, continuing...")
    
    return True

def clean_previous_builds():
    """Clean previous build artifacts"""
    print("\n🧹 Cleaning previous builds...")
    
    # Remove directories
    dirs_to_remove = ["build", "dist", "__pycache__"]
    for dir_name in dirs_to_remove:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✅ Removed {dir_name}")
    
    # Remove spec files
    for spec_file in Path(".").glob("*.spec"):
        spec_file.unlink()
        print(f"✅ Removed {spec_file}")

def build_executable():
    """Build optimized executable"""
    print("\n🚀 Building optimized executable...")
    
    build_command = """python -m PyInstaller --onefile --noconsole \
--hidden-import=faster_whisper \
--hidden-import=PyQt5.QtCore \
--hidden-import=PyQt5.QtGui \
--hidden-import=PyQt5.QtWidgets \
--hidden-import=pyaudio \
--hidden-import=numpy \
--hidden-import=firebase_admin \
--hidden-import=wave \
--hidden-import=threading \
--hidden-import=json \
--hidden-import=traceback \
--exclude-module=torch \
--exclude-module=tensorflow \
--exclude-module=matplotlib \
--exclude-module=scipy \
--exclude-module=pandas \
--exclude-module=opencv-python \
--exclude-module=PIL \
--exclude-module=sklearn \
--exclude-module=seaborn \
--exclude-module=plotly \
--optimize=2 \
--strip \
--name=InterV_AI_Optimized \
live.py"""
    
    return run_command(build_command, "Building executable")

def get_file_size(filepath):
    """Get file size in MB"""
    if os.path.exists(filepath):
        size_bytes = os.path.getsize(filepath)
        size_mb = round(size_bytes / (1024 * 1024), 2)
        return size_mb
    return 0

def main():
    """Main build process"""
    print("=" * 60)
    print("🎯 InterV AI - Complete Build Script")
    print("🚀 Performance Optimized Version")
    print("=" * 60)
    
    # Check if live.py exists
    if not os.path.exists("live.py"):
        print("❌ Error: live.py not found in current directory")
        sys.exit(1)
    
    # Step 1: Install packages
    if not install_packages():
        print("❌ Package installation failed")
        sys.exit(1)
    
    # Step 2: Clean previous builds
    clean_previous_builds()
    
    # Step 3: Build executable
    if not build_executable():
        print("❌ Build failed")
        sys.exit(1)
    
    # Step 4: Check results
    exe_path = "dist/InterV_AI_Optimized.exe"
    if os.path.exists(exe_path):
        file_size = get_file_size(exe_path)
        print("\n" + "=" * 60)
        print("🎉 BUILD SUCCESSFUL!")
        print("=" * 60)
        print(f"📁 Location: {exe_path}")
        print(f"📊 Size: {file_size} MB")
        print("🚀 Features:")
        print("   ✅ Firebase Authentication")
        print("   ✅ Real-time Speech Recognition")
        print("   ✅ AI Response Generation")
        print("   ✅ Performance Optimized")
        print("   ✅ White Background Theme")
        print("   ✅ Time Tracking")
        print("\n💡 Usage: Double-click InterV_AI_Optimized.exe to run")
        print("=" * 60)
    else:
        print("❌ Build completed but executable not found")
        sys.exit(1)

if __name__ == "__main__":
    main()
