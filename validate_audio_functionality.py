#!/usr/bin/env python3
"""
Validation script for the enhanced live.py audio functionality
Tests the key improvements without running the full GUI application
"""

import sys
import time
import pyaudio

def test_audio_device_detection():
    """Test automatic audio device detection"""
    print("🔍 Testing automatic audio device detection...")
    
    try:
        # Import the AudioDeviceManager from live.py
        from live import AudioDeviceManager
        
        # Create device manager
        device_manager = AudioDeviceManager()
        
        # Test default device detection
        default_device = device_manager.get_system_default_input_device()
        if default_device is not None:
            print(f"✅ Default input device detected: {default_device}")
            
            # Get device info
            device_info = device_manager.get_device_info(default_device)
            if device_info:
                print(f"   Device name: {device_info['name']}")
                print(f"   Max input channels: {device_info['maxInputChannels']}")
                print(f"   Default sample rate: {device_info['defaultSampleRate']}")
            
            return True
        else:
            print("❌ No default input device found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing device detection: {e}")
        return False

def test_device_enumeration():
    """Test device enumeration functionality"""
    print("\n📋 Testing device enumeration...")
    
    try:
        from live import AudioDeviceManager
        
        device_manager = AudioDeviceManager()
        devices = device_manager.get_available_input_devices()
        
        print(f"✅ Found {len(devices)} input devices:")
        for i, (device_index, device_name) in enumerate(devices[:5]):  # Show first 5
            print(f"   {i+1}. Device {device_index}: {device_name}")
        
        if len(devices) > 5:
            print(f"   ... and {len(devices) - 5} more devices")
        
        return len(devices) > 0
        
    except Exception as e:
        print(f"❌ Error testing device enumeration: {e}")
        return False

def test_audio_stream_creation():
    """Test creating audio stream with automatic device detection"""
    print("\n🎤 Testing audio stream creation...")
    
    try:
        from live import AudioDeviceManager
        
        device_manager = AudioDeviceManager()
        default_device = device_manager.get_system_default_input_device()
        
        if default_device is None:
            print("❌ No device available for stream test")
            return False
        
        # Test creating audio stream
        audio = pyaudio.PyAudio()
        stream = audio.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=16000,
            input=True,
            frames_per_buffer=1024,
            input_device_index=default_device
        )
        
        print("✅ Audio stream created successfully")
        
        # Test reading a small amount of data
        try:
            data = stream.read(1024, exception_on_overflow=False)
            print(f"✅ Successfully read {len(data)} bytes of audio data")
            success = True
        except Exception as e:
            print(f"⚠️ Warning reading audio data: {e}")
            success = False
        
        # Clean up
        stream.stop_stream()
        stream.close()
        audio.terminate()
        device_manager.cleanup()
        
        return success
        
    except Exception as e:
        print(f"❌ Error testing audio stream: {e}")
        return False

def test_realtime_transcriber_integration():
    """Test that RealTimeTranscriber can be instantiated with new functionality"""
    print("\n🤖 Testing RealTimeTranscriber integration...")
    
    try:
        from live import RealTimeTranscriber
        
        # Create transcriber instance (this tests the new __init__ method)
        transcriber = RealTimeTranscriber()
        
        # Check if device manager was initialized
        if hasattr(transcriber, 'device_manager'):
            print("✅ AudioDeviceManager integrated successfully")
            
            # Check if current device was detected
            if hasattr(transcriber, 'current_device_index'):
                device_index = transcriber._get_current_device_index()
                if device_index is not None:
                    print(f"✅ Current device index: {device_index}")
                    success = True
                else:
                    print("⚠️ No current device detected")
                    success = False
            else:
                print("❌ current_device_index attribute missing")
                success = False
        else:
            print("❌ AudioDeviceManager not integrated")
            success = False
        
        # Clean up
        try:
            transcriber.stop_transcription()
        except:
            pass
        
        return success
        
    except Exception as e:
        print(f"❌ Error testing RealTimeTranscriber integration: {e}")
        return False

def main():
    """Run all validation tests"""
    print("🚀 Audio Functionality Validation")
    print("=" * 50)
    
    tests = [
        ("Audio Device Detection", test_audio_device_detection),
        ("Device Enumeration", test_device_enumeration),
        ("Audio Stream Creation", test_audio_stream_creation),
        ("RealTimeTranscriber Integration", test_realtime_transcriber_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n🏁 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! The audio device functionality is working correctly.")
        print("💡 The live.py application should now work without requiring manual stereo mix configuration!")
    else:
        print(f"\n⚠️ {len(results) - passed} test(s) failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
