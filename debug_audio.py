#!/usr/bin/env python3
"""
Debug script to test system audio capture with real audio detection
"""

import sys
import time
import numpy as np
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

try:
    from live import SystemAudioCapture, PYAUDIO_WPATCH_AVAILABLE
    print("✅ Successfully imported SystemAudioCapture")
except ImportError as e:
    print(f"❌ Failed to import SystemAudioCapture: {e}")
    sys.exit(1)

def test_real_audio_detection():
    """Test system audio capture with real audio detection"""
    print("\n🔍 Testing real audio detection...")
    print("📢 Please play some audio (music, video, etc.) and speak or make sounds")
    print("🔄 Testing for 30 seconds...")
    
    try:
        # Initialize system audio capture
        system_audio = SystemAudioCapture()
        
        if not system_audio.is_available():
            print("❌ System audio capture not available")
            return False
        
        # Open stream
        if not system_audio.open_stream():
            print("❌ Failed to open audio stream")
            return False
        
        print("✅ Audio stream opened successfully")
        print(f"   Sample Rate: {system_audio.RATE} Hz")
        print(f"   Channels: {system_audio.CHANNELS}")
        
        # Audio detection settings
        SILENCE_THRESHOLD = 200
        chunk_count = 0
        audio_detected_count = 0
        max_amplitude = 0
        
        start_time = time.time()
        duration = 30  # Test for 30 seconds
        
        print(f"\n🎧 Listening for audio... (0/{duration}s)")
        
        while time.time() - start_time < duration:
            audio_chunk = system_audio.read_audio_chunk()
            if audio_chunk:
                chunk_count += 1
                
                # Convert to numpy array for analysis
                audio_data = np.frombuffer(audio_chunk, dtype=np.int16)
                amplitude = np.max(np.abs(audio_data))
                max_amplitude = max(max_amplitude, amplitude)
                
                # Check if audio is above silence threshold
                if amplitude > SILENCE_THRESHOLD:
                    audio_detected_count += 1
                    print(f"🔊 Audio detected! Amplitude: {amplitude} (chunk {chunk_count})")
                
                # Progress update every 5 seconds
                elapsed = int(time.time() - start_time)
                if elapsed % 5 == 0 and chunk_count % 100 == 0:
                    print(f"🔄 Progress: {elapsed}/{duration}s - Chunks: {chunk_count}, Audio detected: {audio_detected_count}, Max amplitude: {max_amplitude}")
            
            time.sleep(0.01)  # Small delay
        
        # Close stream
        system_audio.close_stream()
        
        print(f"\n📊 Test Results:")
        print(f"   Total chunks processed: {chunk_count}")
        print(f"   Audio detected chunks: {audio_detected_count}")
        print(f"   Max amplitude recorded: {max_amplitude}")
        print(f"   Detection rate: {(audio_detected_count/chunk_count*100):.1f}%" if chunk_count > 0 else "   No chunks processed")
        
        if audio_detected_count > 0:
            print("✅ System audio capture is working! Audio was detected.")
            return True
        else:
            print("⚠️ No audio detected. Either no audio was playing or there's an issue.")
            if max_amplitude == 0:
                print("❌ No audio signal at all - possible capture issue")
            else:
                print(f"ℹ️ Audio signal present but below threshold ({SILENCE_THRESHOLD})")
            return False
        
    except Exception as e:
        print(f"❌ Error during audio detection test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 System Audio Detection Test")
    print("=" * 50)
    
    # Test PyAudioWPatch availability
    if not PYAUDIO_WPATCH_AVAILABLE:
        print("❌ PyAudioWPatch not available - system audio capture will not work")
        return False
    
    print("✅ PyAudioWPatch is available")
    
    # Test real audio detection
    success = test_real_audio_detection()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ System audio capture is working correctly!")
        print("💡 The live.py application should now be able to detect and transcribe audio.")
    else:
        print("❌ System audio capture test failed.")
        print("💡 Try playing some audio/video and run the test again.")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
